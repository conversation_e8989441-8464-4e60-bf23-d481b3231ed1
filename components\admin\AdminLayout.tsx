import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { toast } from 'react-toastify';
import AdminSidebar from './AdminSidebar';
import AdminHeader from './AdminHeader';
import MobileBottomNav from './mobile/MobileBottomNav';
// import MobileHamburgerMenu from './mobile/MobileHamburgerMenu';
import PWAManager from './PWAManager';
import { useAuth } from '../../hooks/useAuth';
import styles from '../../styles/admin/AdminLayout.module.css';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    // Check if mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 1024);
      if (window.innerWidth <= 1024) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!loading && !user) {
      router.push('/admin/login');
    }
  }, [user, loading, router]);

  const handleLogout = async () => {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        localStorage.removeItem('admin-token');
        toast.success('Logged out successfully');
        router.push('/admin/login');
      } else {
        throw new Error('Logout failed');
      }
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      localStorage.removeItem('admin-token');
      router.push('/admin/login');
    }
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleMobileMenu = () => {
    console.log('📱 Mobile menu toggle:', {
      current: mobileMenuOpen,
      willBe: !mobileMenuOpen,
      isMobile
    });
    setMobileMenuOpen(!mobileMenuOpen);
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading admin portal...</p>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <PWAManager>
      <div className={styles.adminLayout}>
      {/* Sidebar */}
      <AdminSidebar
        user={user}
        collapsed={sidebarCollapsed}
        onToggle={toggleSidebar}
        isMobile={isMobile}
      />

      {/* Main Content Area */}
      <div className={`${styles.mainContent} ${sidebarCollapsed ? styles.sidebarCollapsed : ''}`}>
        {/* Header */}
        <AdminHeader
          user={user}
          onLogout={handleLogout}
          onToggleSidebar={isMobile ? toggleMobileMenu : toggleSidebar}
          sidebarCollapsed={sidebarCollapsed}
        />

        {/* Page Content */}
        <main className={styles.pageContent}>
          {children}
        </main>

        {/* Footer */}
        <footer className={styles.adminFooter}>
          <div className={styles.footerContent}>
            <div className={styles.footerLeft}>
              <span>© 2024 Ocean Soul Sparkles Admin Portal</span>
              <span className={styles.version}>v1.0.0</span>
            </div>
            <div className={styles.footerRight}>
              <Link href="/admin/help" className={styles.footerLink}>
                Help
              </Link>
              <Link href="/admin/privacy" className={styles.footerLink}>
                Privacy
              </Link>
              <Link href="/admin/terms" className={styles.footerLink}>
                Terms
              </Link>
            </div>
          </div>
        </footer>
      </div>

      {/* Mobile Overlay */}
      {isMobile && mobileMenuOpen && (
        <div
          className={styles.mobileOverlay}
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Mobile Bottom Navigation */}
      <div className={styles.mobileBottomNav}>
        <MobileBottomNav userRole={user?.role || 'Admin'} />
      </div>

      {/* Mobile Hamburger Menu */}
      {/* <MobileHamburgerMenu
        isOpen={mobileMenuOpen}
        onClose={() => setMobileMenuOpen(false)}
        userRole={user?.role || 'Admin'}
        userName={`${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'Admin User'}
      /> */}

      {/* Debug Mobile Indicator */}
      <div className={styles.debugMobile}>
        Mobile: {isMobile ? 'YES' : 'NO'} | Width: {typeof window !== 'undefined' ? window.innerWidth : 'N/A'}
      </div>

      {/* Security Notice */}
      <div className={styles.securityBanner}>
        <div className={styles.securityIcon}>🔒</div>
        <span>Secure Admin Portal - All actions are logged and monitored</span>
      </div>
      </div>
    </PWAManager>
  );
}
